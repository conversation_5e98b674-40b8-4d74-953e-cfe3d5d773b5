/**
 * Game Manager - Handles loading and displaying game data from JSON
 */

class GameManager {
    constructor() {
        this.gamesData = null;
        this.siteConfig = null;
        this.currentGame = null;
    }

    /**
     * Load configuration and game data
     */
    async loadData() {
        try {
            // Load site configuration
            const configResponse = await fetch('config/site-config.json');
            this.siteConfig = await configResponse.json();

            // Load games data
            const gamesResponse = await fetch('data/games-data.json');
            this.gamesData = await gamesResponse.json();

            return true;
        } catch (error) {
            console.error('Error loading data:', error);
            return false;
        }
    }

    /**
     * Get all games
     */
    getAllGames() {
        return this.gamesData ? this.gamesData.games : [];
    }

    /**
     * Get game by ID
     */
    getGameById(gameId) {
        if (!this.gamesData) return null;
        return this.gamesData.games.find(game => game.id === gameId);
    }

    /**
     * Search games by name
     */
    searchGames(query) {
        if (!this.gamesData) return [];
        const lowercaseQuery = query.toLowerCase();
        return this.gamesData.games.filter(game => 
            game.name.toLowerCase().includes(lowercaseQuery) ||
            game.currency_name.toLowerCase().includes(lowercaseQuery)
        );
    }

    /**
     * Get games by category
     */
    getGamesByCategory(category) {
        if (!this.gamesData) return [];
        return this.gamesData.games.filter(game => game.category === category);
    }

    /**
     * Set current game (for game.html and finalstep.html)
     */
    setCurrentGame(gameId) {
        this.currentGame = this.getGameById(gameId);
        if (this.currentGame) {
            localStorage.setItem('currentGameId', gameId);
        }
    }

    /**
     * Get current game from localStorage
     */
    getCurrentGame() {
        if (!this.currentGame) {
            const gameId = localStorage.getItem('currentGameId');
            if (gameId) {
                this.currentGame = this.getGameById(gameId);
            }
        }
        return this.currentGame;
    }

    /**
     * Render games grid for index.html
     */
    renderGamesGrid(containerId) {
        const container = document.getElementById(containerId);
        if (!container || !this.gamesData) return;

        const games = this.getAllGames();
        container.innerHTML = '';

        games.forEach(game => {
            const gameElement = this.createGameElement(game);
            container.appendChild(gameElement);
        });
    }

    /**
     * Create game element HTML
     */
    createGameElement(game) {
        const gameDiv = document.createElement('div');
        gameDiv.className = 'tweaked-apps-grid-item';
        
        gameDiv.innerHTML = `
            <div class="tweaked-apps-grid-item-background">
                <div class="tweaked-apps-grid-item-content">
                    <div class="game-icon-container">
                        <img src="${game.game_icon}" class="game-icon" alt="${game.name}"/>
                    </div>
                    <div class="game-name">${game.name}</div>
                    <div class="game-currency">${game.currency_name}</div>
                    <div class="game-button">
                        <a href="pages/game.html" onclick="gameManager.setCurrentGame('${game.id}')">start</a>
                    </div>
                </div>
            </div>
        `;

        return gameDiv;
    }

    /**
     * Update page title and meta tags
     */
    updatePageMeta(pageType = 'home') {
        if (!this.siteConfig) return;

        const config = this.siteConfig;
        
        // Update title
        let title = config.site.title;
        if (pageType === 'game' && this.currentGame) {
            title = `${this.currentGame.name} ${this.currentGame.currency_name} Generator - ${config.site.title}`;
        } else if (pageType === 'final') {
            title = `${config.content_locker.final_step_title} - ${config.site.title}`;
        }
        document.title = title;

        // Update meta description
        const metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            let description = config.site.description;
            if (pageType === 'game' && this.currentGame) {
                description = this.currentGame.description;
            }
            metaDesc.setAttribute('content', description);
        }

        // Update favicon
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon) {
            favicon.setAttribute('href', config.site.favicon);
        }
    }

    /**
     * Initialize search functionality
     */
    initializeSearch() {
        const searchInput = document.querySelector('.quicksearch');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length > 0) {
                const results = this.searchGames(query);
                this.displaySearchResults(results);
            } else {
                this.renderGamesGrid('games-container');
            }
        });
    }

    /**
     * Display search results
     */
    displaySearchResults(results) {
        const container = document.getElementById('games-container');
        if (!container) return;

        container.innerHTML = '';
        
        if (results.length === 0) {
            container.innerHTML = '<div class="no-results">No games found</div>';
            return;
        }

        results.forEach(game => {
            const gameElement = this.createGameElement(game);
            container.appendChild(gameElement);
        });
    }
}

// Global instance
const gameManager = new GameManager();
