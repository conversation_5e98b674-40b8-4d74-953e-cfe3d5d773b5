<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
	<title id="page-title">Final Step - Complete Verification</title>
	<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
	<meta name="description" content="Complete the final step to receive your free currency!" id="page-description" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="icon" type="image/ico" href="../website-resources/images/site-icons/favicon.ico" />
	<!-- Open Graph Meta Tags-->
	<meta property="og:title" content="Final Step - Complete Verification" id="og-title" />
	<meta property="og:description" content="Complete the final step to receive your free currency!" id="og-description" />
	<meta property="og:type" content="website" />
	<meta property="og:url" content="finalstep.html" />
	<!-- Twitter Meta -->
	<meta name="twitter:card" content="summary" />
	<meta name="twitter:title" content="Final Step - Complete Verification" id="twitter-title" />
	<meta name="twitter:description" content="Complete the final step to receive your free currency!" id="twitter-description" />
	<!-- Icons -->
	<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" />
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.gstatic.com/">
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&amp;display=swap" rel="stylesheet">
	<!-- CSS -->
	<link href="../website-resources/stylesheets/bootstrap.min.css" rel="stylesheet" />
	<link href="../website-resources/stylesheets/animate.min.css" rel="stylesheet" />
	<link href="../website-resources/stylesheets/style.css" rel="stylesheet" />
	<link href="../website-resources/stylesheets/a-c-c4.css" rel="stylesheet" />
</head>
<body>
	<!-- إزالة الترس الدوار من البداية -->

	<header>
		<div class="container">
			<div class="h-c">
				<img src="../website-resources/images/site-icons/gameicon.png" class="img-fluid l-i" id="game-icon" />
				<h1 id="final-title">Complete Last Step</h1>
				<p id="final-subtitle">Almost There - Final Verification Required</p>
			</div>
		</div>
		<div id="header-particles"></div>
	</header>

	<section class="m-s">
		<div class="container">
			<div class="c-w c-w-l animate__animated animate__bounceIn">
				<div class="s-o-w">
					<div class="s-o">
						<span class="material-icons-two-tone fa-spin">rotate_right</span>
					</div>
				</div>
				<div class="c-i-t">
					<h4 class="c-w-l-t-v animation-delay-100" id="step-title">Last Step</h4>
					<div class="c-w-l-p-v animation-delay-200" id="step-description">
						Please complete one of the verification steps below to finish the process.
					</div>
				</div>

				<!-- Content Locker Boxes -->
				<div class="verification-boxes animation-delay-300" id="content-locker-container">
					<!-- Content locker iframes will be loaded dynamically -->
				</div>
			</div>
		</div>
	</section>

	<div class="bg-o" id="final-background" style="background-image: url('../website-resources/images/site-icons/background.jpg');"></div>

	<!-- Custom Styles -->
	<style>
		/* تعديل container الرئيسي ليكون أكبر من 550×1000 */
		.m-s .container {
			max-width: 1200px !important;
			width: 95vw !important;
			margin: 0 auto;
			position: relative;
			z-index: 2;
		}

		/* تعديل c-w ليأخذ عرض الشاشة كاملاً تقريباً */
		.c-w.c-w-l {
			width: 100% !important;
			max-width: 100% !important;
			min-width: 1000px !important;
			padding: 40px 30px !important;
			margin: 0 auto;
			box-sizing: border-box;
		}

		.verification-boxes {
			display: flex;
			gap: 20px;
			margin: 30px 0;
			flex-wrap: wrap;
			justify-content: center;
		}

		/* تعديل verification-box ليكون 600×950 */
		.verification-box {
			flex: 0 0 auto;
			width: 600px;
			height: 950px;
			background: #fff;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			padding: 10px;
			text-align: center;
			transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			overflow: hidden;
			box-sizing: border-box;
		}

		.verification-box:hover {
			border-color: #9C27B0;
			box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
			transform: translateY(-3px) scale(1.02);
		}

		/* تعديل container1 ليملأ verification-box */
		.container1 {
			width: 100%;
			height: calc(100% - 20px);
			position: relative;
		}

		.container1 iframe {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 8px;
			background: #f8f9fa;
			transition: all 0.3s ease;
		}

		/* تحسين التصميم المتجاوب */
		@media screen and (max-width: 1400px) {
			.c-w.c-w-l {
				min-width: 800px !important;
			}

			.verification-box {
				width: 500px;
				height: 800px;
			}
		}

		@media screen and (max-width: 1200px) {
			.verification-boxes {
				flex-direction: column;
				gap: 15px;
				align-items: center;
			}

			.verification-box {
				width: 90%;
				max-width: 600px;
				height: 700px;
			}

			.c-w.c-w-l {
				min-width: auto !important;
				width: 95% !important;
			}
		}

		@media screen and (max-width: 768px) {
			.verification-box {
				width: 95%;
				height: 600px;
			}

			.container1 {
				height: calc(100% - 10px);
			}

			.c-w.c-w-l {
				padding: 20px 15px !important;
			}
		}

		@media screen and (max-width: 480px) {
			.verification-box {
				height: 500px;
			}
		}
	</style>

	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
	<script type="text/javascript" src="../website-resources/scripts/particles.min.js"></script>
	<script type="text/javascript" src="../main-scripts/games-management-system.js"></script>

	<!-- Initialize Final Step Page -->
	<script type="text/javascript">
		$(document).ready(async function() {
			// Load data and initialize
			const loaded = await gameManager.loadData();
			if (loaded) {
				// Get current game from localStorage
				const currentGame = gameManager.getCurrentGame();

				if (currentGame) {
					// Update page title and meta
					document.getElementById('page-title').textContent = `Final Step - ${currentGame.name} ${currentGame.currency_name}`;
					document.getElementById('page-description').setAttribute('content', `Complete the final step to receive your ${currentGame.name} ${currentGame.currency_name}!`);
					document.getElementById('og-title').setAttribute('content', `Final Step - ${currentGame.name} ${currentGame.currency_name}`);
					document.getElementById('og-description').setAttribute('content', `Complete the final step to receive your ${currentGame.name} ${currentGame.currency_name}!`);
					document.getElementById('twitter-title').setAttribute('content', `Final Step - ${currentGame.name} ${currentGame.currency_name}`);
					document.getElementById('twitter-description').setAttribute('content', `Complete the final step to receive your ${currentGame.name} ${currentGame.currency_name}!`);

					// Update header content
					document.getElementById('game-icon').src = `../${currentGame.game_icon}`;
					document.getElementById('game-icon').alt = currentGame.name;
					document.getElementById('final-title').textContent = gameManager.siteConfig.content_locker.final_step_title;
					document.getElementById('final-subtitle').textContent = gameManager.siteConfig.content_locker.final_step_subtitle;
					document.getElementById('step-description').textContent = gameManager.siteConfig.content_locker.verification_text;

					// Update background
					if (currentGame.background_image) {
						document.getElementById('final-background').style.backgroundImage = `url('../${currentGame.background_image}')`;
					}

					// Load content locker iframes
					const container = document.getElementById('content-locker-container');
					const links = currentGame.content_locker_links || gameManager.siteConfig.content_locker.default_links;

					links.forEach(link => {
						const boxDiv = document.createElement('div');
						boxDiv.className = 'verification-box';
						boxDiv.innerHTML = `
							<div class="container1">
								<iframe src="${link}" frameborder="0"></iframe>
							</div>
						`;
						container.appendChild(boxDiv);
					});
				} else {
					// No game selected, redirect to home
					window.location.href = '../main-homepage.html';
				}
			} else {
				console.error('Failed to load game data');
			}

			// Initialize particles with smooth animation
			if($('#header-particles').length && typeof particlesJS !== 'undefined'){
				particlesJS('header-particles', {
					"particles": {
						"number": {"value": 60, "density": {"enable": true, "value_area": 800}},
						"color": {"value": "#ffffff"},
						"shape": {"type": "circle"},
						"opacity": {"value": 0.4, "random": false},
						"size": {"value": 3, "random": true},
						"line_linked": {"enable": true, "distance": 150, "color": "#ffffff", "opacity": 0.3, "width": 1},
						"move": {"enable": true, "speed": 4, "direction": "none", "random": false, "straight": false, "out_mode": "out", "bounce": false}
					},
					"interactivity": {
						"detect_on": "canvas",
						"events": {"onhover": {"enable": true, "mode": "repulse"}, "onclick": {"enable": true, "mode": "push"}, "resize": true},
						"modes": {"repulse": {"distance": 150, "duration": 0.4}, "push": {"particles_nb": 2}}
					},
					"retina_detect": true
				});
			}
		});
	</script>
</body>
</html>
