<!--


It is forbidden to re-sell this landing page without Author Permission.

 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
    
<head>
        <title>7top.online</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta name="description" content="Download Unlock Apps for Android and iOS"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <link rel="icon" type="image/ico" href="website-resources/images/site-icons/favicon.ico"/>
        <!-- Open Graph Meta Tags-->
        <meta property="og:title" content="7top.online - Game Currency Generator"/>
        <!-- Title which is displayed when your site is shared on social networks -->
        <meta property="og:description" content="Generate free currencies for your favorite mobile games"/>
        <!-- Website description which is displayed when your site is shared on social networks -->
        <meta property="og:type" content="website"/>
        <meta property="og:url" content="https://7top.online"/>
        <!-- Your Website URL -->
        <meta property="og:image" content="website-resources/images/site-icons/og-image.png"/>
        <!-- Absolute Path to the Image which will display, when your website is shared on social networks -->
        <!-- Twitter Meta -->
        <meta name="twitter:card" content="summary"/>
        <meta name="twitter:site" content="@7toponline"/>
        <meta name="twitter:title" content="7top.online - Game Currency Generator"/>
        <meta name="twitter:description" content="Generate free currencies for your favorite mobile games"/>
        <meta name="twitter:image" content="website-resources/images/site-icons/og-image.png"/>
        <!-- Icons -->
        <link rel="stylesheet" href="../cdn.linearicons.com/free/1.0.0/icon-font.min.css">
        <link rel="stylesheet" href="../use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">
        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <!-- CSS -->
        <link href="website-resources/stylesheets/bootstrap.min.css" rel="stylesheet"/>
        <link href="website-resources/stylesheets/animate.css" rel="stylesheet"/>
        <link href="website-resources/stylesheets/style.css" rel="stylesheet"/>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6">
                    <div class="left-side-wrapper">
                        <header>
                            <img src="website-resources/images/site-icons/app-instal-icon.png" class="img-fluid app-instal-icon animated bounceIn"/>
                            <div class="h-intro">
                                <h1 class="animated bounceIn animation-delay-200"><span>7top.online</span> </h1>
                                <p class="animated bounceIn animation-delay-400">Generating Currencies for Android and iOS.</p>
                            </div>
                        </header>
                        <div class="search-section animated bounceIn animation-delay-600">
                            <div class="search-content">
                                <h3>Find your app</h3>
                                <div class="input-icon-wrapper">
                                    <i class="fas fa-search"></i>
                                    <input type="text" class="quicksearch input-style" placeholder="Search for apps..."/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-side-wrapper">
                        <section class="tweaked-apps-section animated fadeIn animation-delay-800">
                            <div id="app-particles"></div>
                            <div class="tweaked-apps-header">
                                <h2>The First Site in Generating Currencies</h2>
                            </div>
                            <div class="tweaked-apps-content" id="games-container">
                                <!-- Games will be loaded dynamically from JSON -->
                            </div>
                </div>
            </div>
        </div>
    </body>
    <!-- JS -->
    <script type="text/javascript" src="website-resources/scripts/jquery.min.js"></script>
    <script type="text/javascript" src="website-resources/scripts/isotope.pkgd.min.js"></script>
    <script type="text/javascript" src="website-resources/scripts/particles.min.js"></script>
    <script type="text/javascript" src="website-resources/scripts/main.js"></script>
    <script type="text/javascript" src="main-scripts/games-management-system.js"></script>

    <!-- Initialize Game Manager -->
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', async function() {
            // Load data and initialize
            const loaded = await gameManager.loadData();
            if (loaded) {
                // Update page meta information
                gameManager.updatePageMeta('home');

                // Render games grid
                gameManager.renderGamesGrid('games-container');

                // Initialize search functionality
                gameManager.initializeSearch();
            } else {
                console.error('Failed to load game data');
                document.getElementById('games-container').innerHTML =
                    '<div class="error-message">Failed to load games. Please refresh the page.</div>';
            }
        });
    </script>


</html>
