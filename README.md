# Game Currency Generator Website

## نظرة عامة
موقع لتوليد العملات المجانية للألعاب المحمولة مع نظام إدارة محتوى قائم على JSON.

## هيكل المشروع

```
├── config/
│   └── site-config.json          # إعدادات الموقع العامة
├── data/
│   └── games-data.json           # بيانات الألعاب والعملات
├── pages/
│   ├── game.html                 # صفحة اللعبة
│   └── finalstep.html           # صفحة التحقق النهائية
├── assets/
│   ├── css/                     # ملفات التنسيق
│   ├── js/                      # ملفات JavaScript
│   ├── img/
│   │   ├── games/               # صور الألعاب
│   │   ├── backgrounds/         # صور الخلفيات
│   │   ├── currencies/          # صور العملات
│   │   └── icons/               # الأيقونات
│   └── sound/                   # الملفات الصوتية
├── js/
│   └── game-manager.js          # مدير الألعاب الرئيسي
└── index.html                   # الصفحة الرئيسية

```

## الميزات الجديدة

### 1. نظام JSON للبيانات
- **site-config.json**: يحتوي على إعدادات الموقع العامة
- **games-data.json**: يحتوي على جميع بيانات الألعاب

### 2. إدارة ديناميكية للمحتوى
- تحميل الألعاب تلقائياً من JSON
- تحديث المحتوى بدون تعديل HTML
- نظام بحث متقدم

### 3. هيكل منظم للملفات
- مجلدات منفصلة للصور حسب النوع
- ملفات JavaScript منظمة
- صفحات منفصلة في مجلد pages

## كيفية إضافة لعبة جديدة

### 1. إضافة بيانات اللعبة في games-data.json:

```json
{
  "id": "game_id",
  "name": "اسم اللعبة",
  "currency_name": "اسم العملة",
  "currency_amounts": ["100", "500", "1000", "2500"],
  "game_icon": "assets/img/games/game-icon.png",
  "background_image": "assets/img/backgrounds/game-bg.jpg",
  "currency_icon": "assets/img/currencies/currency-icon.png",
  "content_locker_links": [
    "رابط الكونتنت لوكر الأول",
    "رابط الكونتنت لوكر الثاني"
  ],
  "description": "وصف اللعبة",
  "category": "فئة اللعبة"
}
```

### 2. إضافة الصور المطلوبة:
- صورة اللعبة في `assets/img/games/`
- صورة الخلفية في `assets/img/backgrounds/`
- صورة العملة في `assets/img/currencies/`

### 3. حفظ الملف وإعادة تحميل الموقع

## تخصيص إعدادات الموقع

يمكنك تعديل `config/site-config.json` لتغيير:
- عنوان الموقع ووصفه
- الألوان والخطوط
- روابط الكونتنت لوكر الافتراضية
- إعدادات التحليلات

## الملفات المحذوفة/المنقولة

- `All game.txt` و `currency.txt` - تم دمجهما في `games-data.json`
- `game.html` و `finalstep.html` - تم نقلهما إلى مجلد `pages/`
- صور الألعاب - تم نقلها وتنظيمها في مجلدات فرعية

## التشغيل

1. تأكد من وجود جميع الملفات في المكان الصحيح
2. افتح `index.html` في المتصفح
3. ستظهر الألعاب تلقائياً من ملف JSON

## المتطلبات

- متصفح حديث يدعم ES6+ و Fetch API
- خادم ويب (للتطوير المحلي يمكن استخدام Live Server)

## الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة الكود أو التواصل مع المطور.
